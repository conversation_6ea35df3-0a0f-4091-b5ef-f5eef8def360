import sqlite3 from 'sqlite3';
import path from 'path';
import { app } from 'electron';

// تحديد مسار قاعدة البيانات - في مجلد userData للتطبيق
const getDBPath = () => {
  if (typeof window !== 'undefined') {
    // في المتصفح - استخدم مسار مؤقت
    return './furniture_factory.db';
  }

  try {
    // في Electron - استخدم مجلد userData
    const userDataPath = app.getPath('userData');
    return path.join(userDataPath, 'furniture_factory.db');
  } catch (error) {
    // fallback للتطوير
    return path.join(process.cwd(), 'furniture_factory.db');
  }
};

const DB_PATH = getDBPath();

// إنشاء اتصال قاعدة البيانات
export const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', err.message);
  } else {
    console.log('تم الاتصال بقاعدة البيانات بنجاح');
    initializeDatabase();
  }
});

// إنشاء الجداول
const initializeDatabase = () => {
  // جدول المواد
  db.run(`
    CREATE TABLE IF NOT EXISTS materials (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      pricePerSqm REAL NOT NULL,
      category TEXT NOT NULL,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول العمال
  db.run(`
    CREATE TABLE IF NOT EXISTS workers (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      specialty TEXT NOT NULL,
      pricePerSqm REAL NOT NULL,
      phone TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول المصانع
  db.run(`
    CREATE TABLE IF NOT EXISTS factories (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      specialty TEXT NOT NULL,
      pricePerSqm REAL NOT NULL,
      location TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول المصممين
  db.run(`
    CREATE TABLE IF NOT EXISTS designers (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      specialty TEXT NOT NULL,
      pricePerSqm REAL NOT NULL,
      phone TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول العملاء
  db.run(`
    CREATE TABLE IF NOT EXISTS customers (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      phone TEXT,
      email TEXT,
      address TEXT,
      totalProjects INTEGER DEFAULT 0,
      totalSpent REAL DEFAULT 0,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول المشاريع
  db.run(`
    CREATE TABLE IF NOT EXISTS projects (
      id TEXT PRIMARY KEY,
      customerName TEXT NOT NULL,
      customerPhone TEXT,
      area REAL NOT NULL,
      furnitureType TEXT NOT NULL,
      materialId TEXT NOT NULL,
      workerId TEXT NOT NULL,
      factoryId TEXT NOT NULL,
      designerId TEXT NOT NULL,
      totalCost REAL NOT NULL,
      materialCost REAL NOT NULL,
      workerCost REAL NOT NULL,
      factoryCost REAL NOT NULL,
      designerCost REAL NOT NULL,
      paidAmount REAL DEFAULT 0,
      remainingAmount REAL NOT NULL,
      status TEXT DEFAULT 'قيد التنفيذ',
      invoiceStatus TEXT DEFAULT 'مبدئية',
      notes TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      completedAt DATETIME,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (materialId) REFERENCES materials (id),
      FOREIGN KEY (workerId) REFERENCES workers (id),
      FOREIGN KEY (factoryId) REFERENCES factories (id),
      FOREIGN KEY (designerId) REFERENCES designers (id)
    )
  `);

  // جدول الفواتير
  db.run(`
    CREATE TABLE IF NOT EXISTS invoices (
      id TEXT PRIMARY KEY,
      projectId TEXT NOT NULL,
      customerName TEXT NOT NULL,
      customerPhone TEXT,
      totalAmount REAL NOT NULL,
      status TEXT DEFAULT 'مبدئية',
      type TEXT NOT NULL,
      notes TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (projectId) REFERENCES projects (id)
    )
  `);

  // جدول الموظفين
  db.run(`
    CREATE TABLE IF NOT EXISTS employees (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      position TEXT NOT NULL,
      baseSalary REAL NOT NULL,
      bonuses REAL DEFAULT 0,
      deductions REAL DEFAULT 0,
      totalSalary REAL NOT NULL,
      phone TEXT,
      hireDate DATE,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول معاملات الخزينة
  db.run(`
    CREATE TABLE IF NOT EXISTS cash_transactions (
      id TEXT PRIMARY KEY,
      type TEXT NOT NULL,
      category TEXT NOT NULL,
      amount REAL NOT NULL,
      description TEXT NOT NULL,
      projectId TEXT,
      employeeId TEXT,
      date DATE NOT NULL,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (projectId) REFERENCES projects (id),
      FOREIGN KEY (employeeId) REFERENCES employees (id)
    )
  `);

  console.log('تم إنشاء جداول قاعدة البيانات بنجاح');
};

// وظائف مساعدة لتنفيذ الاستعلامات
export const runQuery = (sql: string, params: any[] = []): Promise<any> => {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
};

export const getQuery = (sql: string, params: any[] = []): Promise<any> => {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
};

export const allQuery = (sql: string, params: any[] = []): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
};

// إغلاق قاعدة البيانات
export const closeDatabase = () => {
  db.close((err) => {
    if (err) {
      console.error('خطأ في إغلاق قاعدة البيانات:', err.message);
    } else {
      console.log('تم إغلاق قاعدة البيانات بنجاح');
    }
  });
};
