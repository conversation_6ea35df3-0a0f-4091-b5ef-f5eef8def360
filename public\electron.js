import { app, BrowserWindow, Menu, shell, ipc<PERSON>ain, dialog } from 'electron';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// استيراد قاعدة البيانات
import { createRequire } from 'module';
import fs from 'fs';
const require = createRequire(import.meta.url);
const { initializeDatabase, runQuery, getQuery, allQuery, closeDatabase } = require('./database-electron.cjs');

// تحديد ما إذا كان في وضع التطوير أم الإنتاج
const distPath = path.join(__dirname, '../dist/index.html');
const isDev = !fs.existsSync(distPath);

console.log('مسار ملف البناء:', distPath);
console.log('هل الملف موجود؟', fs.existsSync(distPath));
console.log('وضع التشغيل:', isDev ? 'تطوير' : 'إنتاج');

// Keep a global reference of the window object
let mainWindow;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.cjs')
    },
    icon: path.join(__dirname, 'icon.png'),
    titleBarStyle: 'default',
    show: false,
    autoHideMenuBar: false
  });

  // Load the app
  let startUrl;
  if (isDev) {
    startUrl = 'http://localhost:5173';
    console.log('تشغيل في وضع التطوير - محاولة الاتصال بالخادم المحلي');
  } else {
    // استخدام مسار مطلق لتجنب مشاكل التشفير
    const indexPath = path.resolve(__dirname, '../dist/index.html');
    // تحويل المسار إلى URL صحيح باستخدام pathToFileURL
    startUrl = require('url').pathToFileURL(indexPath).href;
    console.log('تشغيل في وضع الإنتاج - تحميل الملفات المحلية');
    console.log('مسار الملف:', startUrl);
  }

  mainWindow.loadURL(startUrl);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Open DevTools in development
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Create application menu
  createMenu();
}

function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // Handle new file
          }
        },
        {
          label: 'فتح',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            // Handle open file
          }
        },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            // Handle save
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'إعادة تحميل قسري', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { type: 'separator' },
        { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'نافذة',
      submenu: [
        { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول التطبيق',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'حول التطبيق',
              message: 'نظام إدارة مصنع الأثاث',
              detail: 'نظام متكامل لإدارة جميع عمليات مصنع الأثاث\nالإصدار: 1.0.0',
              buttons: ['موافق']
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(async () => {
  try {
    await initializeDatabase();
    console.log('تم تهيئة قاعدة البيانات بنجاح');
  } catch (error) {
    console.error('خطأ في تهيئة قاعدة البيانات:', error);
  }
  createWindow();
});

app.on('window-all-closed', () => {
  closeDatabase();
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// IPC handlers for communication with renderer process
ipcMain.handle('app-version', () => {
  return app.getVersion();
});

ipcMain.handle('show-save-dialog', async () => {
  const result = await dialog.showSaveDialog(mainWindow, {
    filters: [
      { name: 'JSON Files', extensions: ['json'] },
      { name: 'All Files', extensions: ['*'] }
    ]
  });
  return result;
});

ipcMain.handle('show-open-dialog', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    filters: [
      { name: 'JSON Files', extensions: ['json'] },
      { name: 'All Files', extensions: ['*'] }
    ],
    properties: ['openFile']
  });
  return result;
});

// معالجات قاعدة البيانات
// المواد
ipcMain.handle('db-get-materials', async () => {
  try {
    return await allQuery('SELECT * FROM materials ORDER BY name');
  } catch (error) {
    console.error('خطأ في جلب المواد:', error);
    return [];
  }
});

ipcMain.handle('db-add-material', async (event, material) => {
  try {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO materials (id, name, description, pricePerSqm, category) VALUES (?, ?, ?, ?, ?)',
      [id, material.name, material.description, material.pricePerSqm, material.category]
    );
    return id;
  } catch (error) {
    console.error('خطأ في إضافة المادة:', error);
    return null;
  }
});

// العمال
ipcMain.handle('db-get-workers', async () => {
  try {
    return await allQuery('SELECT * FROM workers ORDER BY name');
  } catch (error) {
    console.error('خطأ في جلب العمال:', error);
    return [];
  }
});

ipcMain.handle('db-add-worker', async (event, worker) => {
  try {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO workers (id, name, specialty, pricePerSqm, phone) VALUES (?, ?, ?, ?, ?)',
      [id, worker.name, worker.specialty, worker.pricePerSqm, worker.phone]
    );
    return id;
  } catch (error) {
    console.error('خطأ في إضافة العامل:', error);
    return null;
  }
});

// المصانع
ipcMain.handle('db-get-factories', async () => {
  try {
    return await allQuery('SELECT * FROM factories ORDER BY name');
  } catch (error) {
    console.error('خطأ في جلب المصانع:', error);
    return [];
  }
});

ipcMain.handle('db-add-factory', async (event, factory) => {
  try {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO factories (id, name, specialty, pricePerSqm, location) VALUES (?, ?, ?, ?, ?)',
      [id, factory.name, factory.specialty, factory.pricePerSqm, factory.location]
    );
    return id;
  } catch (error) {
    console.error('خطأ في إضافة المصنع:', error);
    return null;
  }
});

// المصممين
ipcMain.handle('db-get-designers', async () => {
  try {
    return await allQuery('SELECT * FROM designers ORDER BY name');
  } catch (error) {
    console.error('خطأ في جلب المصممين:', error);
    return [];
  }
});

ipcMain.handle('db-add-designer', async (event, designer) => {
  try {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO designers (id, name, specialty, pricePerSqm, phone) VALUES (?, ?, ?, ?, ?)',
      [id, designer.name, designer.specialty, designer.pricePerSqm, designer.phone]
    );
    return id;
  } catch (error) {
    console.error('خطأ في إضافة المصمم:', error);
    return null;
  }
});
