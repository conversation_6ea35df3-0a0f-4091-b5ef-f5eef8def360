const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { app } = require('electron');

let db = null;

// تحديد مسار قاعدة البيانات
const getDBPath = () => {
  try {
    const userDataPath = app.getPath('userData');
    return path.join(userDataPath, 'furniture_factory.db');
  } catch (error) {
    return path.join(__dirname, '..', 'furniture_factory.db');
  }
};

// إنشاء اتصال قاعدة البيانات
const initializeDatabase = () => {
  return new Promise((resolve, reject) => {
    const dbPath = getDBPath();
    console.log('مسار قاعدة البيانات:', dbPath);
    
    db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('خطأ في الاتصال بقاعدة البيانات:', err.message);
        reject(err);
      } else {
        console.log('تم الاتصال بقاعدة البيانات بنجاح');
        createTables().then(resolve).catch(reject);
      }
    });
  });
};

// إنشاء الجداول
const createTables = () => {
  return new Promise((resolve, reject) => {
    const tables = [
      // جدول المواد
      `CREATE TABLE IF NOT EXISTS materials (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        pricePerSqm REAL NOT NULL,
        category TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول العمال
      `CREATE TABLE IF NOT EXISTS workers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        specialty TEXT NOT NULL,
        pricePerSqm REAL NOT NULL,
        phone TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول المصانع
      `CREATE TABLE IF NOT EXISTS factories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        specialty TEXT NOT NULL,
        pricePerSqm REAL NOT NULL,
        location TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول المصممين
      `CREATE TABLE IF NOT EXISTS designers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        specialty TEXT NOT NULL,
        pricePerSqm REAL NOT NULL,
        phone TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول العملاء
      `CREATE TABLE IF NOT EXISTS customers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        totalProjects INTEGER DEFAULT 0,
        totalSpent REAL DEFAULT 0,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول المشاريع
      `CREATE TABLE IF NOT EXISTS projects (
        id TEXT PRIMARY KEY,
        customerName TEXT NOT NULL,
        customerPhone TEXT,
        area REAL NOT NULL,
        furnitureType TEXT NOT NULL,
        materialId TEXT NOT NULL,
        workerId TEXT NOT NULL,
        factoryId TEXT NOT NULL,
        designerId TEXT NOT NULL,
        totalCost REAL NOT NULL,
        materialCost REAL NOT NULL,
        workerCost REAL NOT NULL,
        factoryCost REAL NOT NULL,
        designerCost REAL NOT NULL,
        paidAmount REAL DEFAULT 0,
        remainingAmount REAL NOT NULL,
        status TEXT DEFAULT 'قيد التنفيذ',
        invoiceStatus TEXT DEFAULT 'مبدئية',
        notes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        completedAt DATETIME,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول الفواتير
      `CREATE TABLE IF NOT EXISTS invoices (
        id TEXT PRIMARY KEY,
        projectId TEXT NOT NULL,
        customerName TEXT NOT NULL,
        customerPhone TEXT,
        totalAmount REAL NOT NULL,
        status TEXT DEFAULT 'مبدئية',
        type TEXT NOT NULL,
        notes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول الموظفين
      `CREATE TABLE IF NOT EXISTS employees (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        position TEXT NOT NULL,
        baseSalary REAL NOT NULL,
        bonuses REAL DEFAULT 0,
        deductions REAL DEFAULT 0,
        totalSalary REAL NOT NULL,
        phone TEXT,
        hireDate DATE,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول معاملات الخزينة
      `CREATE TABLE IF NOT EXISTS cash_transactions (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT NOT NULL,
        projectId TEXT,
        employeeId TEXT,
        date DATE NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    let completed = 0;
    const total = tables.length;

    tables.forEach((sql, index) => {
      db.run(sql, (err) => {
        if (err) {
          console.error(`خطأ في إنشاء الجدول ${index + 1}:`, err.message);
          reject(err);
          return;
        }
        
        completed++;
        if (completed === total) {
          console.log('تم إنشاء جميع الجداول بنجاح');
          resolve();
        }
      });
    });
  });
};

// وظائف مساعدة لتنفيذ الاستعلامات
const runQuery = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
};

const getQuery = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
};

const allQuery = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
};

// إغلاق قاعدة البيانات
const closeDatabase = () => {
  if (db) {
    db.close((err) => {
      if (err) {
        console.error('خطأ في إغلاق قاعدة البيانات:', err.message);
      } else {
        console.log('تم إغلاق قاعدة البيانات بنجاح');
      }
    });
  }
};

module.exports = {
  initializeDatabase,
  runQuery,
  getQuery,
  allQuery,
  closeDatabase
};
