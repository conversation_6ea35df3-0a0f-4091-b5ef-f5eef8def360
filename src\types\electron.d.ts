// تعريف أنواع Electron API
export interface ElectronAPI {
  // App info
  getVersion: () => Promise<string>;
  
  // File operations
  showSaveDialog: () => Promise<any>;
  showOpenDialog: () => Promise<any>;
  
  // Platform info
  platform: string;
  
  // Window controls
  minimize: () => Promise<void>;
  maximize: () => Promise<void>;
  close: () => Promise<void>;
  
  // Data export/import helpers
  exportData: (data: any) => Promise<void>;
  importData: () => Promise<any>;
  
  // Print functionality
  print: () => Promise<void>;
  
  // Notification
  showNotification: (title: string, body: string) => Promise<void>;
  
  // Database operations
  // المواد
  getMaterials: () => Promise<any[]>;
  addMaterial: (material: any) => Promise<string | null>;
  
  // العمال
  getWorkers: () => Promise<any[]>;
  addWorker: (worker: any) => Promise<string | null>;
  
  // المصانع
  getFactories: () => Promise<any[]>;
  addFactory: (factory: any) => Promise<string | null>;
  
  // المصممين
  getDesigners: () => Promise<any[]>;
  addDesigner: (designer: any) => Promise<string | null>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
