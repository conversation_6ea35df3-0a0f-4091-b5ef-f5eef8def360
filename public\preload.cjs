const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getVersion: () => ipcRenderer.invoke('app-version'),
  
  // File operations
  showSaveDialog: () => ipcRenderer.invoke('show-save-dialog'),
  showOpenDialog: () => ipcRenderer.invoke('show-open-dialog'),
  
  // Platform info
  platform: process.platform,
  
  // Window controls
  minimize: () => ipcRenderer.invoke('window-minimize'),
  maximize: () => ipcRenderer.invoke('window-maximize'),
  close: () => ipcRenderer.invoke('window-close'),
  
  // Data export/import helpers
  exportData: (data) => ipcRenderer.invoke('export-data', data),
  importData: () => ipcRenderer.invoke('import-data'),

  // Print functionality
  print: () => ipc<PERSON>enderer.invoke('print-page'),

  // Notification
  showNotification: (title, body) => ipcRenderer.invoke('show-notification', { title, body }),

  // Database operations
  // المواد
  getMaterials: () => ipcRenderer.invoke('db-get-materials'),
  addMaterial: (material) => ipcRenderer.invoke('db-add-material', material),

  // العمال
  getWorkers: () => ipcRenderer.invoke('db-get-workers'),
  addWorker: (worker) => ipcRenderer.invoke('db-add-worker', worker),

  // المصانع
  getFactories: () => ipcRenderer.invoke('db-get-factories'),
  addFactory: (factory) => ipcRenderer.invoke('db-add-factory', factory),

  // المصممين
  getDesigners: () => ipcRenderer.invoke('db-get-designers'),
  addDesigner: (designer) => ipcRenderer.invoke('db-add-designer', designer)
});

// Add some basic security
window.addEventListener('DOMContentLoaded', () => {
  // Disable context menu in production
  if (process.env.NODE_ENV === 'production') {
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
    });
  }
  
  // Disable drag and drop
  document.addEventListener('dragover', (e) => {
    e.preventDefault();
  });
  
  document.addEventListener('drop', (e) => {
    e.preventDefault();
  });
});
