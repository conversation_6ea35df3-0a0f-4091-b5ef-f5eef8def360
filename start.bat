@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    نظام إدارة مصنع الأثاث
echo    تطبيق سطح المكتب
echo ========================================
echo.

echo 🔍 فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    echo 📥 تحميل من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js مثبت
echo.

echo 📦 فحص التبعيات...
if not exist "node_modules" (
    echo 🔄 تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
) else (
    echo ✅ التبعيات مثبتة
)

echo.
echo 🔨 فحص ملفات البناء...
if not exist "dist" (
    echo 🔄 بناء التطبيق...
    npm run build
    if %errorlevel% neq 0 (
        echo ❌ فشل في بناء التطبيق
        pause
        exit /b 1
    )
    echo ✅ تم بناء التطبيق بنجاح
) else (
    echo ✅ ملفات البناء موجودة
)

echo.
echo 🚀 تشغيل التطبيق مباشرة...
echo.
npm run electron

if %errorlevel% neq 0 (
    echo.
    echo ⚠️ فشل التشغيل المباشر، محاولة التشغيل مع خادم التطوير...
    npm run electron:dev
)

pause

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo 🔧 جرب الأوامر التالية:
    echo    npm run clean
    echo    npm install
    echo    npm run electron:dev
    pause
)
